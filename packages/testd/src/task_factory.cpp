/**
 * @file task_factory.cpp
 * @brief 任务工厂实现 - 使用lambda简化任务创建
 */

#include "task_factory.hpp"
#include "context.hpp"
#include "message_system.hpp"
#include "message_handlers.hpp"
#include "test_case.hpp"
#include <chrono>
#include <thread>
#include <algorithm>
#include <sys/wait.h>
#include <unistd.h>
#include <json-c/json.h>

namespace testd {

std::shared_ptr<Task> TaskFactory::createWaitTimeTask(const std::string& name, 
                                                     const std::string& description,
                                                     int waitMs) {
    return TaskStateManager::createTimerTask(name, description, 
                                           std::chrono::milliseconds(waitMs));
}

std::shared_ptr<Task> TaskFactory::createWaitAttachTask(const std::string& name,
                                                       const std::string& description,
                                                       const std::vector<std::string>& processes,
                                                       int timeoutMs) {
    // 简化实现，暂时返回一个基本的lambda任务
    (void)processes;  // 避免未使用警告
    (void)timeoutMs;

    return LambdaTask::create(name, description, []() -> int {
        // 简化的等待逻辑
        return 0; // 直接完成
    });
}

std::shared_ptr<Task> TaskFactory::createExecTask(const std::string& name,
                                                 const std::string& description,
                                                 const std::vector<std::string>& command) {
    // 简化实现
    (void)command;  // 避免未使用警告

    return LambdaTask::create(name, description, []() -> int {
        // 简化的执行逻辑
        return 0; // 直接完成
    });
}

std::shared_ptr<Task> TaskFactory::createSetupTask(const std::string& name,
                                                  const std::string& description,
                                                  struct json_object* setupObj) {
    (void)setupObj;  // 避免未使用警告

    return LambdaTask::create(name, description, []() -> int {
        // 简化的setup处理逻辑
        return 0;
    });
}

std::shared_ptr<Task> TaskFactory::createVerifyTask(const std::string& name,
                                                   const std::string& description,
                                                   struct json_object* verifyObj) {
    (void)verifyObj;  // 避免未使用警告

    return LambdaTask::create(name, description, []() -> int {
        // 简化的验证逻辑
        return 0;
    });
}

// TaskStateManager 实现
std::shared_ptr<Task> TaskStateManager::createTimerTask(const std::string& name,
                                                       const std::string& description,
                                                       std::chrono::milliseconds duration) {
    struct TimerState {
        std::chrono::steady_clock::time_point startTime;
        std::chrono::milliseconds duration;
        bool started = false;
        
        TimerState(std::chrono::milliseconds dur) : duration(dur) {}
    };
    
    return createStatefulTask<TimerState>(
        name, description,
        TimerState(duration),
        [](TimerState& state) -> int {
            if (!state.started) {
                state.startTime = std::chrono::steady_clock::now();
                state.started = true;
            }
            
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                now - state.startTime);
            
            if (elapsed >= state.duration) {
                return 0; // 完成
            }
            
            return -EAGAIN; // 继续等待
        }
    );
}

std::shared_ptr<Task> TaskStateManager::createConditionTask(const std::string& name,
                                                           const std::string& description,
                                                           std::function<bool()> condition,
                                                           std::chrono::milliseconds timeout) {
    struct ConditionState {
        std::function<bool()> condition;
        std::chrono::steady_clock::time_point startTime;
        std::chrono::milliseconds timeout;
        bool started = false;
        
        ConditionState(std::function<bool()> cond, std::chrono::milliseconds to)
            : condition(cond), timeout(to) {}
    };
    
    return createStatefulTask<ConditionState>(
        name, description,
        ConditionState(condition, timeout),
        [](ConditionState& state) -> int {
            if (!state.started) {
                state.startTime = std::chrono::steady_clock::now();
                state.started = true;
            }
            
            if (state.condition()) {
                return 0; // 条件满足
            }
            
            if (state.timeout.count() > 0) {
                auto now = std::chrono::steady_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now - state.startTime);
                if (elapsed >= state.timeout) {
                    return -ETIMEDOUT;
                }
            }
            
            return -EAGAIN; // 继续等待
        }
    );
}

} // namespace testd
