/**
 * @file context.cpp
 * @brief 全局上下文类实现 - C++11版本
 */

#include "context.hpp"
#include "message_system.hpp"
#include "socket.hpp"
#include "test_case.hpp"
#include "json_protocol.hpp"
#include "message_handlers.hpp"
#include "task_display.hpp"
#include <iostream>
#include <getopt.h>
#include <cstring>
#include <memory>
#include <sys/select.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <thread>
#include <chrono>
#include "task_display.hpp"

namespace testd {

// Config类实现
Config::Config() : tcpPort(8000), verbose(false), configFile(""), outputFile(""), sysroot("") {
}

bool Config::parseArgs(int argc, char** argv, CommandType& cmdType,
                      std::vector<std::string>& testFiles,
                      pid_t& targetPid, std::string& functionName, struct json_object*& params) {
    static struct option long_options[] = {
        {"port", required_argument, 0, 'p'},
        {"verbose", no_argument, 0, 'v'},
        {"config", required_argument, 0, 'c'},
        {"output", required_argument, 0, 'o'},
        {"sysroot", required_argument, 0, 's'},
        {"help", no_argument, 0, 'h'},
        {"version", no_argument, 0, 'V'},
        {0, 0, 0, 0}
    };

    int opt;
    int option_index = 0;
    cmdType = CommandType::NONE;

    while ((opt = getopt_long(argc, argv, "p:vc:o:s:hV", long_options, &option_index)) != -1) {
        switch (opt) {
            case 'p':
                tcpPort = std::stoi(optarg);
                break;
            case 'v':
                verbose = true;
                break;
            case 'c':
                configFile = optarg;
                break;
            case 'o':
                outputFile = optarg;
                break;
            case 's':
                sysroot = optarg;
                break;
            case 'h':
                printHelp();
                return false;
            case 'V':
                printVersion();
                return false;
            case '?':
                return false;
            default:
                break;
        }
    }

    // 处理命令
    if (optind < argc) {
        std::string command = argv[optind];
        
        if (command == "server") {
            cmdType = CommandType::SERVER;
        } else if (command == "run" && optind + 1 < argc) {
            cmdType = CommandType::RUN_TEST;
            // 支持多个测试文件
            for (int i = optind + 1; i < argc; i++) {
                std::string arg = argv[i];
                if (arg.length() > 0 && arg[0] == '-') {
                    break; // 遇到选项参数，停止收集文件
                }
                testFiles.push_back(arg);
            }
            if (testFiles.empty()) {
                std::cerr << "No test files specified" << std::endl;
                return false;
            }
        } else if (command == "mock" && optind + 2 < argc) {
            cmdType = CommandType::MOCK_FUNCTION;
            targetPid = std::stoi(argv[optind + 1]);
            functionName = argv[optind + 2];
            
            // 如果有参数，解析JSON
            if (optind + 3 < argc) {
                params = json_tokener_parse(argv[optind + 3]);
                if (!params) {
                    std::cerr << "Invalid JSON parameters" << std::endl;
                    return false;
                }
            }
        } else if (command == "call" && optind + 2 < argc) {
            cmdType = CommandType::CALL_FUNCTION;
            targetPid = std::stoi(argv[optind + 1]);
            functionName = argv[optind + 2];
            
            // 如果有参数，解析JSON
            if (optind + 3 < argc) {
                params = json_tokener_parse(argv[optind + 3]);
                if (!params) {
                    std::cerr << "Invalid JSON parameters" << std::endl;
                    return false;
                }
            }
        } else {
            std::cerr << "Unknown command: " << command << std::endl;
            printHelp();
            return false;
        }
    } else {
        std::cerr << "No command specified" << std::endl;
        printHelp();
        return false;
    }

    return true;
}

void Config::printHelp() {
    std::cout << "Usage: testd [选项] [命令]\n\n";
    std::cout << "命令:\n";
    std::cout << "  run <测试用例文件>     运行指定测试用例\n";
    std::cout << "  server                启动控制服务器\n";
    std::cout << "  mock <进程ID> <函数>  直接Mock指定进程的函数\n";
    std::cout << "  call <进程ID> <函数>  直接调用指定进程的函数\n";
    std::cout << "\n";
    std::cout << "选项:\n";
    std::cout << "  -p, --port <端口>     指定TCP控制端口（默认8000）\n";
    std::cout << "  -v, --verbose         详细输出模式\n";
    std::cout << "  -c, --config <文件>   指定配置文件\n";
    std::cout << "  -o, --output <文件>   指定输出文件\n";
    std::cout << "  -s, --sysroot <路径>  指定系统根目录路径\n";
    std::cout << "  -h, --help            显示帮助信息\n";
    std::cout << "  -V, --version         显示版本信息\n";
}

void Config::printVersion() {
    std::cout << "testd version 1.0.0" << std::endl;
}

// ClientConnection类实现
ClientConnection::ClientConnection(int socketFd, pid_t pid, const std::string& programName, Private)
    : socketFd(socketFd), pipeFd{-1, -1, -1}, pid(pid), isChild(false), exitStatus(0), programName(programName), mode(TestMode::NORMAL),
      savedPid(pid), savedExitStatus(0), processExited(false), isWaitAttach(false) {
    // 生成UUID
    uuid_t uuid_bytes;
    uuid_generate(uuid_bytes);
    char uuid_str[37];
    uuid_unparse(uuid_bytes, uuid_str);
    uuid = std::string(uuid_str);
}

ClientConnection::~ClientConnection() {
    cleanup();
}

void ClientConnection::updateFdSet(fd_set& read_fds, int& max_fd) const {
    if (socketFd > 0) {
        FD_SET(socketFd, &read_fds);
        if (socketFd > max_fd) {
            max_fd = socketFd;
        }
    }
    for (int i = 0; i < 3; i++) {
        if (pipeFd[i] > 0) {
            FD_SET(pipeFd[i], &read_fds);
            if (pipeFd[i] > max_fd) {
                max_fd = pipeFd[i];
            }
        }
    }
}


void ClientConnection::updateClientData(fd_set &read_fds) {
    if (isChild) {
        // 从管道读取数据
        char buffer[1024];
        ssize_t n;
        if (pid > 0) {
            if (waitpid(pid, &exitStatus, WNOHANG) == pid) {
                // 保存进程信息用于后续显示
                savedExitStatus = exitStatus;
                processExited = true;
                pid = 0;
                cleanup();
            }
        }
        if (pipeFd[1] > 0 && FD_ISSET(pipeFd[1], &read_fds)) {
            while ((n = read(pipeFd[1], buffer, sizeof(buffer))) > 0) {
                stdoutBuffer.append(buffer, n);
            }
            if (n == 0) {
                close(pipeFd[1]);
                pipeFd[1] = -1;
            }
        }
        if (pipeFd[2] > 0 && FD_ISSET(pipeFd[2], &read_fds)) {
            while ((n = read(pipeFd[2], buffer, sizeof(buffer))) > 0) {
                stderrBuffer.append(buffer, n);
            }
            if (n == 0) {
                close(pipeFd[2]);
                pipeFd[2] = -1;
            }
        }
    }
    if (socketFd > 0 && FD_ISSET(socketFd, &read_fds)) {
        // 从socket读取数据
        struct json_object* jsonObj = nullptr;
        int ret;
        std::string data;
        ret = Socket::recvFromClient(shared_from_this(), data);
        if (ret == -ECONNRESET) {
            // 客户端关闭连接
            // if (config.isVerbose()) {
            //     std::cout << "Client disconnected, fd=" << client->getSocketFd() << std::endl;
            // }
            cleanup();
        } else if (ret < 0) {
            std::cerr << "Failed to receive JSON from client: " << -ret << std::endl;
            cleanup();
        }
        do{
            ret = Socket::parseJson(&jsonObj, data);

            if (ret < 0) {
                break;
            } else if (ret > 0) {
                // 处理消息 - 现在传递客户端UUID
                JsonProtocol::processMessage(jsonObj, uuid);
                json_object_put(jsonObj);
            }
        }while(ret > 0);
    }
}

void ClientConnection::printClientData(std::ostream& os) {
    os << "============" << programName << "============" << std::endl;
    os << "PID: " << savedPid;
    if (processExited) {
        os << " (exited with status: " << savedExitStatus << ")";
    } else if (pid <= 0) {
        os << " (disconnected)";
    }
    os << std::endl;
    os << "------------stdout------------" << std::endl;
    os << stdoutBuffer << std::endl;
    os << "------------stderr------------" << std::endl;
    os << stderrBuffer << std::endl;
    os << "==============================" << std::endl;
}

void ClientConnection::cleanup() {
    if (socketFd > 0) {
        close(socketFd);
        socketFd = -1;
    }
    for (int i = 0; i < 3; i++) {
        if (pipeFd[i] >= 0) {
            close(pipeFd[i]);
            pipeFd[i] = -1;
        }
    }
}

void ClientConnection::updateClients(ClientMapType& clients, fd_set& read_fds) {
    for (auto it = clients.begin(); it != clients.end();) {
        auto client = it->second;
        client->updateClientData(read_fds);

        if (client->getSocketFd() == -1) {
            // 已经关闭的连接，直接移除
            client->printClientData(std::cout);
            it = clients.erase(it);
            continue;
        }
        ++it;
    }
}

// Context类实现
Context::Context() : running(false) {
}

Context& Context::getInstance() {
    static Context instance;
    return instance;
}

bool Context::init(int argc, char** argv) {
    CommandType cmdType = CommandType::NONE;
    pid_t targetPid = 0;
    std::string functionName;
    struct json_object* params = nullptr;

    // 初始化消息系统
    MessageHandlerFactory::registerDefaultHandlers();

    // 解析命令行参数
    std::vector<std::string> testFiles;
    if (!config.parseArgs(argc, argv, cmdType, testFiles, targetPid, functionName, params)) {
        return false;
    }
    
    // 根据命令类型执行相应操作
    int ret = 0;
    switch (cmdType) {
        case CommandType::SERVER:
            ret = runServer();
            break;
            
        case CommandType::RUN_TEST:
            if (testFiles.size() > 1) {
                ret = runTests(testFiles);
            } else {
                ret = runTest(testFiles[0]);
            }
            break;
            
        case CommandType::MOCK_FUNCTION:
            ret = mockFunction(targetPid, functionName, params);
            break;
            
        case CommandType::CALL_FUNCTION:
            ret = callFunction(targetPid, functionName, params);
            break;
            
        default:
            std::cerr << "Unknown command type" << std::endl;
            ret = -1;
            break;
    }
    
    // 清理参数
    if (params) {
        json_object_put(params);
    }
    
    return ret == 0;
}

void Context::cleanup() {
    // 终止所有子进程（wait_attach的进程除外）
    terminateAllChildProcesses();

    // 关闭所有客户端连接
    clients.clear();

    // 关闭Socket
    unixSocket.reset();
    tcpSocket.reset();

    // 清理测试资源
    currentTest.reset();
    result.reset();
}

int Context::runServer() {
    // 初始化Unix Domain Socket
    //TODO    
    return 0;
}

int Context::runTest(const std::string& testFile) {
    // 加载测试用例
    currentTest = TestCase::loadFromFile(testFile);
    if (!currentTest) {
        std::cerr << "Failed to load test case: " << testFile << std::endl;
        return -1;
    }
    
    // 初始化Unix Domain Socket
    unixSocket = std::make_shared<UnixDomainSocket>();
    if (!unixSocket->init()) {
        std::cerr << "Failed to initialize Unix Domain Socket" << std::endl;
        return -1;
    }
        
    std::cout << "Test started: " << currentTest->getName() <<std::endl;
    TaskTreeDisplay display;
    display.setEnabled(true);
    running = true;
    
    // 主循环
    while (running && currentTest->getState() != Task::State::FINISHED && 
           currentTest->getState() != Task::State::ERROR) {
        // 处理测试用例事件
        int result = currentTest->execute();
        if (result < 0 && result != -EAGAIN) {
            std::cerr << "Test execution failed" << std::endl;
            break;
        }
        display.refresh(currentTest);
        
        // 处理Socket事件
        fd_set read_fds;
        FD_ZERO(&read_fds);
        
        // 添加Unix Domain Socket
        int max_fd = unixSocket->getSocketFd();
        FD_SET(unixSocket->getSocketFd(), &read_fds);
        
        // 添加客户端Socket
        for (const auto& pair : clients) {
            pair.second->updateFdSet(read_fds, max_fd);
        }
        
        // 设置超时
        struct timeval tv;
        tv.tv_sec = 0;
        tv.tv_usec = 100000;  // 100ms
        
        // 等待事件
        int ret = select(max_fd + 1, &read_fds, nullptr, nullptr, &tv);
        if (ret < 0) {
            if (errno == EINTR) {
                continue;
            }
            std::cerr << "select error: " << strerror(errno) << std::endl;
            break;
        }
        display.clear();
        
        // 处理Unix Domain Socket连接
        if (FD_ISSET(unixSocket->getSocketFd(), &read_fds)) {
            auto client = unixSocket->acceptConnection();
            if (client) {
                addClient(client);
                if (config.isVerbose()) {
                    std::cout << "New Unix client connected, fd=" << client->getSocketFd() << std::endl;
                }
            }
        }
        
        // 处理客户端数据
        // for (auto it = clients.begin(); it != clients.end();) {
        //     auto client = it->second;
        //     client->updateClientData(read_fds);

        //     if (client->getSocketFd() == -1) {
        //         // 已经关闭的连接，直接移除
        //         client->printClientData(std::cout);
        //         it = clients.erase(it);
        //         continue;
        //     }
        //     ++it;
        // }
        ClientConnection::updateClients(clients, read_fds);

        // 处理消息系统中的待处理消息
        MessageSystem::getInstance().processMessages();
    }
    display.finalize(currentTest);
    
    // 输出测试结果
    if (currentTest->getState() == Task::State::FINISHED) {
        std::cout << "Test finished: " << currentTest->getName() << std::endl;
        auto result = currentTest->getResult();
        std::cout << "Total cases: " << result->getTotal() << std::endl;
        std::cout << "Passed cases: " << result->getPassed() << std::endl;
        std::cout << "Failed cases: " << result->getFailed() << std::endl;
    } else {
        std::cout << "Test failed: " << currentTest->getName() << std::endl;
    }
    MessageSystem::getInstance().getRouter().clearSubscriptions();
    currentTest.reset();
    MessageSystem::getInstance().removeRouter();
    
    return (currentTest->getState() == Task::State::FINISHED) ? 0 : -1;
}

int Context::runTests(const std::vector<std::string>& testFiles) {
    //TODO
    (void)testFiles;
    return 0;
}


int Context::mockFunction(pid_t pid, const std::string& funcName, struct json_object* params) {
    //TODO
    (void)params;
    (void)pid;
    (void)funcName;
    return 0;
}

int Context::callFunction(pid_t pid, const std::string& funcName, struct json_object* params) {
    // 初始化Unix Domain Socket
    unixSocket = std::make_shared<UnixDomainSocket>();
    if (!unixSocket->init()) {
        std::cerr << "Failed to initialize Unix Domain Socket" << std::endl;
        return -1;
    }
    
    // 等待客户端连接
    std::cout << "Waiting for client connection..." << std::endl;
    running = true;
    
    while (running && clients.empty()) {
        fd_set read_fds;
        FD_ZERO(&read_fds);
        
        // 添加Unix Domain Socket
        FD_SET(unixSocket->getSocketFd(), &read_fds);
        
        // 设置超时
        struct timeval tv;
        tv.tv_sec = 1;
        tv.tv_usec = 0;
        
        // 等待事件
        int ret = select(unixSocket->getSocketFd() + 1, &read_fds, nullptr, nullptr, &tv);
        if (ret < 0) {
            if (errno == EINTR) {
                continue;
            }
            std::cerr << "select error: " << strerror(errno) << std::endl;
            return -1;
        }
        
        // 处理Unix Domain Socket连接
        if (FD_ISSET(unixSocket->getSocketFd(), &read_fds)) {
            auto client = unixSocket->acceptConnection();
            if (client) {
                addClient(client);
                if (config.isVerbose()) {
                    std::cout << "New Unix client connected, fd=" << client->getSocketFd() << std::endl;
                }
            }
        }
    }
    
    if (clients.empty()) {
        std::cerr << "No client connected" << std::endl;
        return -1;
    }
    
    // 查找目标进程
    auto client = unixSocket->findClientByPid(pid);
    if (!client) {
        std::cerr << "Process not found: " << pid << std::endl;
        return -1;
    }
    
    // 创建调用请求
    struct json_object* callRequest = JsonProtocol::createCallRequest(funcName, params);
    if (!callRequest) {
        std::cerr << "Failed to create call request" << std::endl;
        return -1;
    }
    
    // 发送请求
    int ret = unixSocket->sendJson(client, callRequest);
    json_object_put(callRequest);
    
    if (ret < 0) {
        std::cerr << "Failed to send call request: " << -ret << std::endl;
        return -1;
    }
    
    std::cout << "Call function: pid=" << pid << ", function=" << funcName << std::endl;
    return 0;
}

std::shared_ptr<ClientConnection> Context::getClientByUUID(const std::string& uuid) const {
    auto it = clients.find(uuid);
    return (it != clients.end()) ? it->second : nullptr;
}

std::vector<std::shared_ptr<ClientConnection>> Context::getAllClients() const {
    std::vector<std::shared_ptr<ClientConnection>> result;
    for (const auto& pair : clients) {
        result.push_back(pair.second);
    }
    return result;
}

void Context::addClient(std::shared_ptr<ClientConnection> client) {
    if (client) {
        clients[client->getUUID()] = client;
    }
}

void Context::removeClient(const std::string& uuid) {
    clients.erase(uuid);
}

std::shared_ptr<ClientConnection> Context::findClientByProgramName(const std::string& programName) {
    for (const auto& pair : clients) {
        if (pair.second->getProgramName() == programName) {
            return pair.second;
        }
    }
    return nullptr;
}

void Context::terminateAllChildProcesses() {
    for (const auto& pair : clients) {
        auto client = pair.second;

        // 跳过wait_attach的进程
        if (client->getIsWaitAttach()) {
            if (config.isVerbose()) {
                std::cout << "Skipping wait_attach process: "
                          << client->getProgramName()
                          << " (PID: " << client->getPid() << ")" << std::endl;
            }
            continue;
        }

        pid_t pid = client->getPid();
        if (pid > 0) {
            if (config.isVerbose()) {
                std::cout << "Terminating child process: "
                          << client->getProgramName()
                          << " (PID: " << pid << ")" << std::endl;
            }

            // 首先尝试SIGTERM
            if (kill(pid, SIGTERM) == 0) {
                // 等待一段时间让进程优雅退出
                usleep(100000);  // 100ms

                // 检查进程是否还在运行
                if (kill(pid, 0) == 0) {
                    // 进程仍在运行，使用SIGKILL强制终止
                    if (config.isVerbose()) {
                        std::cout << "Process " << pid << " did not respond to SIGTERM, using SIGKILL" << std::endl;
                    }
                    kill(pid, SIGKILL);
                }
            }
        }
    }
}



} // namespace testd
