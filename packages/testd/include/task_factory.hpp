/**
 * @file task_factory.hpp
 * @brief 任务工厂头文件 - 使用lambda简化任务创建
 */

#ifndef TESTD_TASK_FACTORY_HPP
#define TESTD_TASK_FACTORY_HPP

#include "task.hpp"
#include <functional>
#include <chrono>
#include <vector>
#include <memory>

namespace testd {

/**
 * @brief 任务工厂 - 提供常用任务的lambda实现
 */
class TaskFactory {
public:
    /**
     * @brief 创建等待时间任务
     */
    static std::shared_ptr<Task> createWaitTimeTask(const std::string& name, 
                                                   const std::string& description,
                                                   int waitMs);
    
    /**
     * @brief 创建等待进程连接任务
     */
    static std::shared_ptr<Task> createWaitAttachTask(const std::string& name,
                                                     const std::string& description,
                                                     const std::vector<std::string>& processes,
                                                     int timeoutMs = -1);
    
    /**
     * @brief 创建执行命令任务
     */
    static std::shared_ptr<Task> createExecTask(const std::string& name,
                                               const std::string& description,
                                               const std::vector<std::string>& command);
    
    /**
     * @brief 创建简单的设置任务
     */
    static std::shared_ptr<Task> createSetupTask(const std::string& name,
                                                const std::string& description,
                                                struct json_object* setupObj);
    
    /**
     * @brief 创建验证任务
     */
    static std::shared_ptr<Task> createVerifyTask(const std::string& name,
                                                 const std::string& description,
                                                 struct json_object* verifyObj);
    
    /**
     * @brief 创建自定义lambda任务
     */
    template<typename Func>
    static std::shared_ptr<Task> createCustomTask(const std::string& name,
                                                 const std::string& description,
                                                 Func&& func) {
        return LambdaTask::create(name, description, std::forward<Func>(func), true, true);
    }
    
private:
    TaskFactory() = default;
};

/**
 * @brief 任务状态管理器 - 使用mutable捕获管理状态
 */
class TaskStateManager {
public:
    /**
     * @brief 创建带状态的任务
     */
    template<typename StateType>
    static std::shared_ptr<Task> createStatefulTask(const std::string& name,
                                                   const std::string& description,
                                                   StateType initialState,
                                                   std::function<int(StateType&)> executeFunc) {
        // 使用mutable捕获状态
        auto state = std::make_shared<StateType>(std::move(initialState));
        
        return LambdaTask::create(name, description, 
            [state, executeFunc]() mutable -> int {
                return executeFunc(*state);
            },
            true, true
        );
    }
    
    /**
     * @brief 创建计时器任务
     */
    static std::shared_ptr<Task> createTimerTask(const std::string& name,
                                                const std::string& description,
                                                std::chrono::milliseconds duration);
    
    /**
     * @brief 创建条件等待任务
     */
    static std::shared_ptr<Task> createConditionTask(const std::string& name,
                                                    const std::string& description,
                                                    std::function<bool()> condition,
                                                    std::chrono::milliseconds timeout = std::chrono::milliseconds(-1));
};

} // namespace testd

#endif // TESTD_TASK_FACTORY_HPP
